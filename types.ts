import { MD3Theme } from 'react-native-paper';

export interface AppTheme extends MD3Theme {
  colors: MD3Theme['colors'] & {
    accent: string;
    background: string;
    surface: string;
    text: string;
    placeholder: string;
    backdrop: string;
    notification: string;
  };
  myOwnProperty: boolean; // Example of extending Theme itself
}

export interface Visa {
  id: string;
  visa_type: string;
  status: 'open' | 'closed';
  center: string;
  country_code: string;
  mission_code: string;
  category: string;
  last_available_date: string;
  last_checked_at: string;
}

export interface FilterOptions {
  center: string;
  country: string | undefined;
  mission: string | undefined;
  category: string | undefined;
  type: string | undefined;
  status: 'open' | 'closed' | undefined;
}