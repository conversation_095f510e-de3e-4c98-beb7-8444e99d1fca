import * as React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { Provider as PaperProvider, DefaultTheme } from 'react-native-paper';
import VisaListScreen from './screens/VisaListScreen';
import { AppTheme } from './types';

const Stack = createStackNavigator();

const theme: AppTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: '#4F46E5', // Indigo
    accent: '#0D9488', // Teal
    background: '#F9FAFB', // Light Gray
    surface: '#FFFFFF', // White for cards/surfaces
    text: '#374151', // Dark Gray for text
    placeholder: '#9CA3AF', // Gray for placeholders
    backdrop: 'rgba(0, 0, 0, 0.5)', // For modals
    notification: '#EF4444', // Red for errors/notifications
  },
  myOwnProperty: true, // Add the dummy property from the extended Theme
};

export default function App() {
  return (
    <PaperProvider theme={theme}>
      <NavigationContainer>
        <Stack.Navigator initialRouteName="VisaList">
          <Stack.Screen name="VisaList" component={VisaListScreen} options={{ headerShown: false }} />
        </Stack.Navigator>
      </NavigationContainer>
    </PaperProvider>
  );
}
