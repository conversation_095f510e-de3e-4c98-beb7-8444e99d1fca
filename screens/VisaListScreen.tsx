import React, { useState, useEffect, useCallback } from 'react';
import { View, FlatList, StyleSheet, RefreshControl } from 'react-native';
import { Appbar, Text, Button } from 'react-native-paper';
import VisaCard from '../components/VisaCard';
import LoadingIndicator from '../components/LoadingIndicator';
import ErrorDisplay from '../components/ErrorDisplay';
import FilterModal from '../components/FilterModal';
import { fetchVisaData } from '../services/api';
import { Visa, FilterOptions } from '../types';
import { useAppTheme } from '../hooks/useAppTheme';

const VisaListScreen: React.FC = () => {
  const theme = useAppTheme();
  const [visas, setVisas] = useState<Visa[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [filterModalVisible, setFilterModalVisible] = useState<boolean>(false);
  const [filters, setFilters] = useState<FilterOptions>({
    center: '',
    country: '',
    mission: '',
    category: '',
    type: '',
    status: 'open' as 'open' | 'closed' | undefined, // Default status filter to 'open'
  });

  const [availableCountries, setAvailableCountries] = useState<string[]>([]);
  const [availableMissions, setAvailableMissions] = useState<string[]>([]);
  const [availableCategories, setAvailableCategories] = useState<string[]>([]);
  const [availableTypes, setAvailableTypes] = useState<string[]>([]);

  const extractUniqueOptions = (data: Visa[]) => {
    const countries = Array.from(new Set(data.map(visa => visa.country_code))).sort();
    const missions = Array.from(new Set(data.map(visa => visa.mission_code))).sort();
    const categories = Array.from(new Set(data.map(visa => visa.category))).sort();
    const types = Array.from(new Set(data.map(visa => visa.visa_type))).sort();
    
    setAvailableCountries(countries);
    setAvailableMissions(missions);
    setAvailableCategories(categories);
    setAvailableTypes(types);
  };

  const loadVisaData = useCallback(async () => {
    setError(null);
    try {
      const data = await fetchVisaData();
      if (Array.isArray(data?.data?.visas)) {
        setVisas(data?.data?.visas);
        extractUniqueOptions(data?.data?.visas); // Extract options after fetching data
      } else {
        console.warn('API returned non-array data:', data);
        setVisas([]); // Ensure visas is always an array
        extractUniqueOptions([]); // Clear options if data is not an array
      }
    } catch (err) {
      setError('Failed to fetch visa data. Please try again later.');
      console.error(err);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    loadVisaData();
  }, [loadVisaData]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadVisaData();
  }, [loadVisaData]);

  const applyFilters = (newFilters: FilterOptions) => {
    setFilters(newFilters);
    setFilterModalVisible(false);
  };

  const clearFilters = () => {
    setFilters({
      center: '',
      country: undefined, // Changed to undefined for "All" option
      mission: undefined, // Changed to undefined for "All" option
      category: undefined, // Changed to undefined for "All" option
      type: undefined, // Changed to undefined for "All" option
      status: 'open',
    });
    setFilterModalVisible(false);
  };

  const filteredVisas = visas.filter(visa => {
    const matchesCenter = filters.center ? visa.center.toLowerCase().includes(filters.center.toLowerCase()) : true;
    const matchesCountry = filters.country ? visa.country_code.toLowerCase() === filters.country.toLowerCase() : true;
    const matchesMission = filters.mission ? visa.mission_code.toLowerCase() === filters.mission.toLowerCase() : true;
    const matchesCategory = filters.category ? visa.category.toLowerCase() === filters.category.toLowerCase() : true;
    const matchesType = filters.type ? visa.visa_type.toLowerCase() === filters.type.toLowerCase() : true;
    const matchesStatus = filters.status ? visa.status === filters.status : true;

    return matchesCenter && matchesCountry && matchesMission && matchesCategory && matchesType && matchesStatus;
  });

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Appbar.Header style={{ backgroundColor: theme.colors.primary }}>
        <Appbar.Content title="VisaList" color={theme.colors.surface} />
        <Appbar.Action icon="filter" color={theme.colors.surface} onPress={() => setFilterModalVisible(true)} />
      </Appbar.Header>

      {loading ? (
        <LoadingIndicator />
      ) : error ? (
        <ErrorDisplay message={error} onRetry={loadVisaData} />
      ) : filteredVisas.length === 0 ? (
        <View style={styles.noResultsContainer}>
          <Text style={styles.noResultsText}>No results found.</Text>
          <Button mode="outlined" onPress={clearFilters} style={styles.clearFiltersButton}>
            Clear Filters
          </Button>
        </View>
      ) : (
        <FlatList
          data={filteredVisas}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => <VisaCard visa={item} />}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
        />
      )}

      <FilterModal
        visible={filterModalVisible}
        onDismiss={() => setFilterModalVisible(false)}
        currentFilters={filters}
        onApplyFilters={applyFilters}
        onClearFilters={clearFilters}
        availableCountries={availableCountries}
        availableMissions={availableMissions}
        availableCategories={availableCategories}
        availableTypes={availableTypes}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContent: {
    padding: 16,
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  noResultsText: {
    fontSize: 18,
    marginBottom: 10,
    textAlign: 'center',
  },
  clearFiltersButton: {
    marginTop: 10,
  },
});

export default VisaListScreen;
