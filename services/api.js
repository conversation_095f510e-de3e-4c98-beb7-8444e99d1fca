const API_BASE_URL = 'https://api.visasbot.com/api/visa/list';

export const fetchVisaData = async () => {
  try {
    const response = await fetch(API_BASE_URL);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching visa data:", error);
    throw error;
  }
};