import React from 'react';
import { View, StyleSheet } from 'react-native';
import { ActivityIndicator, Text } from 'react-native-paper';
import { useAppTheme } from '../hooks/useAppTheme';

const LoadingIndicator: React.FC = () => {
  const theme = useAppTheme();
  return (
    <View style={styles.container}>
      <ActivityIndicator animating={true} color={theme.colors.primary} size="large" />
      <Text style={[styles.text, { color: theme.colors.text }]}>Loading visa data...</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  text: {
    marginTop: 10,
    fontSize: 16,
  },
});

export default LoadingIndicator;