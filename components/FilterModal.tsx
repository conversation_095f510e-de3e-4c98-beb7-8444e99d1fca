import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Modal, Portal, Text, Button, TextInput, Divider } from 'react-native-paper';
import { Picker } from '@react-native-picker/picker';
import { FilterOptions } from '../types';
import { useAppTheme } from '../hooks/useAppTheme';

interface FilterModalProps {
  visible: boolean;
  onDismiss: () => void;
  currentFilters: FilterOptions;
  onApplyFilters: (filters: FilterOptions) => void;
  onClearFilters: () => void;
  availableCountries: string[];
  availableMissions: string[];
  availableCategories: string[];
  availableTypes: string[];
}

const FilterModal: React.FC<FilterModalProps> = ({
  visible,
  onDismiss,
  currentFilters,
  onApplyFilters,
  onClearFilters,
  availableCountries,
  availableMissions,
  availableCategories,
  availableTypes,
}) => {
  const theme = useAppTheme();
  const [filters, setFilters] = useState<FilterOptions>(currentFilters);

  useEffect(() => {
    setFilters(currentFilters);
  }, [currentFilters]);

  const handleApply = () => {
    onApplyFilters(filters);
  };

  const handleClear = () => {
    onClearFilters();
  };

  const containerStyle = { backgroundColor: theme.colors.surface, padding: 20, margin: 20, borderRadius: 8 };

  const statuses = ['open', 'closed'];

  return (
    <Portal>
      <Modal visible={visible} onDismiss={onDismiss} contentContainerStyle={containerStyle}>
        <ScrollView>
          <Text style={[styles.title, { color: theme.colors.text }]}>Filter Visa Appointments</Text>
          <Divider style={styles.divider} />

          <TextInput
            label="Center Search"
            value={filters.center}
            onChangeText={(text) => setFilters({ ...filters, center: text })}
            mode="outlined"
            style={styles.input}
            // In React Native Paper v5 (MD3), underlineColor is not directly available in the theme prop for TextInput.
            // You can control the outline color via the 'outlineColor' prop or global theme.
            // For now, removing it to resolve the error.
            theme={{ colors: { primary: theme.colors.primary } }}
          />

          <Text style={[styles.pickerLabel, { color: theme.colors.text }]}>Country:</Text>
          <View style={[styles.pickerContainer, { borderColor: theme.colors.placeholder }]}>
            <Picker
              selectedValue={filters.country}
              onValueChange={(itemValue: string | undefined) => setFilters({ ...filters, country: itemValue })}
              style={styles.picker}
            >
              <Picker.Item label="All Countries" value={undefined} />
              {availableCountries.map((country) => (
                <Picker.Item key={country} label={country} value={country} />
              ))}
            </Picker>
          </View>

          <Text style={[styles.pickerLabel, { color: theme.colors.text }]}>Mission:</Text>
          <View style={[styles.pickerContainer, { borderColor: theme.colors.placeholder }]}>
            <Picker
              selectedValue={filters.mission}
              onValueChange={(itemValue: string | undefined) => setFilters({ ...filters, mission: itemValue })}
              style={styles.picker}
            >
              <Picker.Item label="All Missions" value={undefined} />
              {availableMissions.map((mission) => (
                <Picker.Item key={mission} label={mission} value={mission} />
              ))}
            </Picker>
          </View>

          <Text style={[styles.pickerLabel, { color: theme.colors.text }]}>Category:</Text>
          <View style={[styles.pickerContainer, { borderColor: theme.colors.placeholder }]}>
            <Picker
              selectedValue={filters.category}
              onValueChange={(itemValue: string | undefined) => setFilters({ ...filters, category: itemValue })}
              style={styles.picker}
            >
              <Picker.Item label="All Categories" value={undefined} />
              {availableCategories.map((category) => (
                <Picker.Item key={category} label={category} value={category} />
              ))}
            </Picker>
          </View>

          <Text style={[styles.pickerLabel, { color: theme.colors.text }]}>Type:</Text>
          <View style={[styles.pickerContainer, { borderColor: theme.colors.placeholder }]}>
            <Picker
              selectedValue={filters.type}
              onValueChange={(itemValue: string | undefined) => setFilters({ ...filters, type: itemValue })}
              style={styles.picker}
            >
              <Picker.Item label="All Types" value={undefined} />
              {availableTypes.map((type) => (
                <Picker.Item key={type} label={type} value={type} />
              ))}
            </Picker>
          </View>

          <Text style={[styles.pickerLabel, { color: theme.colors.text }]}>Status:</Text>
          <View style={[styles.pickerContainer, { borderColor: theme.colors.placeholder }]}>
            <Picker
              selectedValue={filters.status}
              onValueChange={(itemValue: 'open' | 'closed') => setFilters({ ...filters, status: itemValue })}
              style={styles.picker}
            >
              <Picker.Item label="All Statuses" value={undefined} />
              <Picker.Item label="All Statuses" value={undefined} />
              {statuses.map((status) => (
                <Picker.Item key={status} label={status.charAt(0).toUpperCase() + status.slice(1)} value={status} />
              ))}
            </Picker>
          </View>

          <View style={styles.buttonContainer}>
            <Button mode="outlined" onPress={handleClear} style={styles.button}>
              Clear Filters
            </Button>
            <Button mode="contained" onPress={handleApply} style={styles.button}>
              Apply Filters
            </Button>
          </View>
        </ScrollView>
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  divider: {
    marginBottom: 16,
  },
  input: {
    marginBottom: 16,
  },
  pickerLabel: {
    fontSize: 16,
    marginBottom: 8,
    marginTop: 8,
  },
  pickerContainer: {
    borderWidth: 1,
    borderRadius: 4,
    marginBottom: 16,
    overflow: 'hidden', // Ensures the picker doesn't overflow the border radius
  },
  picker: {
    height: 50,
    width: '100%',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 20,
  },
  button: {
    flex: 1,
    marginHorizontal: 8,
  },
});

export default FilterModal;