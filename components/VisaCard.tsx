import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Card, Chip, Text } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { Visa } from '../types';

interface VisaCardProps {
  visa: Visa;
}

const VisaCard: React.FC<VisaCardProps> = ({ visa }) => {
  const statusColor = visa.status === 'open' ? '#0D9488' : '#6B7280';
  const statusIcon = visa.status === 'open' ? 'check-circle' : 'close-circle';

  return (
    <Card style={styles.card}>
      <Card.Content style={styles.cardContent}>
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <Text style={styles.title}>{visa.visa_type}</Text>
            <Text style={styles.centerText}>{visa.center}</Text>
          </View>
          <Chip
            icon={statusIcon}
            style={[styles.statusChip, { backgroundColor: statusColor }]}
            textStyle={styles.statusText}
          >
            {visa.status.toUpperCase()}
          </Chip>
        </View>

        <View style={styles.detailsContainer}>
          <View style={styles.detailRow}>
            <Icon name="earth" size={16} color="#9CA3AF" />
            <Text style={styles.detailText}>
              {visa.country_code} - {visa.mission_code}
            </Text>
          </View>
          <View style={styles.detailRow}>
            <Icon name="file-document" size={16} color="#9CA3AF" />
            <Text style={styles.detailText}>
              {visa.category}
            </Text>
          </View>
        </View>

        <View style={styles.footer}>
          <Icon name="calendar" size={14} color="#6B7280" />
          <Text style={styles.dateText}>
            Available: {visa.last_available_date || 'N/A'} | Checked: {visa.last_checked_at}
          </Text>
        </View>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginVertical: 8,
    borderRadius: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    backgroundColor: '#1F2937',
    borderWidth: 1,
    borderColor: '#374151',
  },
  cardContent: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  titleContainer: {
    flex: 1,
    marginRight: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
    lineHeight: 24,
  },
  centerText: {
    fontSize: 14,
    color: '#9CA3AF',
    lineHeight: 20,
  },
  statusChip: {
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  statusText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 12,
  },
  detailsContainer: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#D1D5DB',
    flex: 1,
  },
  footer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#374151',
  },
  dateText: {
    marginLeft: 6,
    fontSize: 12,
    color: '#6B7280',
    flex: 1,
  },
});

export default VisaCard;