import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Card, Title, Paragraph, Chip, Text } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { Visa } from '../types';
import { useAppTheme } from '../hooks/useAppTheme';

interface VisaCardProps {
  visa: Visa;
}

const VisaCard: React.FC<VisaCardProps> = ({ visa }) => {
  const theme = useAppTheme();

  const statusColor = visa.status === 'open' ? theme.colors.accent : theme.colors.backdrop;
  const statusIcon = visa.status === 'open' ? 'check-circle' : 'close-circle';

  return (
    <Card style={styles.card}>
      <Card.Content>
        <View style={styles.header}>
          <Title>{visa.visa_type}</Title>
          <Chip
            icon={() => <Icon name={statusIcon} size={16} color={theme.colors.surface} />}
            style={{ backgroundColor: statusColor }}
            textStyle={{ color: theme.colors.surface }}
          >
            {visa.status.toUpperCase()}
          </Chip>
        </View>
        <Paragraph style={styles.centerText}>{visa.center}</Paragraph>
        <View style={styles.detailsContainer}>
          <View style={styles.detailItem}>
            <Icon name="earth" size={16} color={theme.colors.text} />
            <Text style={styles.detailText}>{visa.country_code}</Text>
          </View>
          <View style={styles.detailItem}>
            <Icon name="file-document" size={16} color={theme.colors.text} />
            <Text style={styles.detailText}>{visa.mission_code}</Text>
          </View>
          <View style={styles.detailItem}>
            <Icon name="tag" size={16} color={theme.colors.text} />
            <Text style={styles.detailText}>{visa.category}</Text>
          </View>
        </View>
        <View style={styles.footer}>
          <Icon name="calendar" size={14} color={theme.colors.placeholder} />
          <Text style={styles.dateText}>
            Last Available: {visa.last_available_date || 'N/A'} | Checked: {visa.last_checked_at}
          </Text>
        </View>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginVertical: 8,
    borderRadius: 8,
    elevation: 2, // For Android shadow
    shadowColor: '#000', // For iOS shadow
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  centerText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  detailsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 4,
  },
  detailText: {
    marginLeft: 4,
    fontSize: 14,
  },
  footer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#EEE',
    paddingTop: 8,
  },
  dateText: {
    marginLeft: 4,
    fontSize: 12,
    color: '#666',
  },
});

export default VisaCard;